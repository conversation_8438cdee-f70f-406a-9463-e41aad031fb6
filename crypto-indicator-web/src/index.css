/* CSS Custom Properties for Comfortable Dark Theme */
:root {
  /* Comfortable Dark Base Colors (inspired by Cat<PERSON>uccin/Night Owl) */
  --bg-primary: #1e1e2e;
  --bg-secondary: #181825;
  --bg-surface: #313244;
  --bg-elevated: #45475a;

  /* Subtle Glass Effects */
  --glass-bg: rgba(255, 255, 255, 0.05);
  --glass-bg-strong: rgba(255, 255, 255, 0.08);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
  --glass-blur: blur(12px);
  --glass-blur-strong: blur(16px);

  /* Comfortable Background Gradients */
  --dark-bg-primary: var(--bg-primary);
  --dark-bg-secondary: var(--bg-secondary);
  --dark-bg-gradient: linear-gradient(135deg, #1e1e2e 0%, #181825 50%, #313244 100%);

  /* Comfortable Text Colors */
  --text-primary: #cdd6f4;
  --text-secondary: #bac2de;
  --text-muted: #9399b2;

  /* Soft Accent Colors (muted and comfortable) */
  --accent-blue: #89b4fa;
  --accent-green: #a6e3a1;
  --accent-yellow: #f9e2af;
  --accent-red: #f38ba8;
  --accent-purple: #cba6f7;
  --accent-teal: #94e2d5;

  /* Legacy color mappings for compatibility */
  --neon-cyan: var(--accent-teal);
  --neon-blue: var(--accent-blue);
  --neon-purple: var(--accent-purple);
  --neon-pink: var(--accent-red);
  --neon-green: var(--accent-green);
  --neon-yellow: var(--accent-yellow);
  --accent-cyan: var(--accent-teal);

  /* Subtle Border and Interaction Colors */
  --border-primary: #45475a;
  --border-hover: #585b70;
  --border-active: #6c7086;

  /* Removed aggressive glow effects - using subtle shadows instead */
  --subtle-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  --hover-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: var(--bg-primary);
  height: 100vh;
  color: var(--text-primary);
  overflow: hidden;
}

.app-container {
  height: 100vh;
  padding: 20px;
  position: relative;
  background: var(--dark-bg-gradient);
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.app-container::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(148, 226, 213, 0.03) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(203, 166, 247, 0.02) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(137, 180, 250, 0.02) 0%, transparent 60%);
  pointer-events: none;
  z-index: -1;
}

.header {
  margin-bottom: 30px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--border-primary);
  border-radius: 16px;
  padding: 20px 24px;
  box-shadow: var(--glass-shadow);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.header:hover {
  border-color: var(--border-hover);
  box-shadow: var(--hover-shadow);
}

.header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 2.2rem;
  color: var(--text-primary);
  margin: 0;
  font-weight: 700;
}

.lightning-icon {
  font-size: 2.4rem;
  color: var(--accent-yellow);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.lightning-icon:hover {
  transform: scale(1.05);
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.4));
}

.title-text {
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
}

.header-status {
  display: flex;
  align-items: center;
}

.live-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(166, 227, 161, 0.1);
  border: 1px solid var(--accent-green);
  border-radius: 20px;
  padding: 4px 12px;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--accent-green);
}

.live-dot {
  width: 6px;
  height: 6px;
  background: var(--accent-green);
  border-radius: 50%;
  animation: livePulse 2s ease-in-out infinite;
}

@keyframes livePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

.live-text {
  letter-spacing: 0.5px;
}

.header-subtitle {
  color: var(--text-secondary);
  font-size: 1rem;
  margin: 0;
  font-weight: 500;
  opacity: 0.9;
}

.table-container {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--border-primary);
  border-radius: 24px;
  overflow-x: auto;
  overflow-y: visible;
  -webkit-overflow-scrolling: touch;
  box-shadow: var(--glass-shadow);
  position: relative;
  transition: all 0.3s ease;
}

.table-container:hover {
  border-color: var(--border-hover);
  box-shadow: var(--hover-shadow);
}

.table-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-purple), transparent);
  opacity: 0.3;
}

.stats-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(148, 226, 213, 0.03);
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-primary);
}

.stats-info button {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  padding: 10px 20px;
  border-radius: 12px;
  cursor: pointer;
  margin-left: 15px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.stats-info button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.stats-info button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table {
  width: 100%;
  min-width: 700px;
  border-collapse: collapse;
  font-size: 14px;
  background: transparent;
  table-layout: auto;
  position: relative;
}

/* Sticky first column for all screen sizes */
.table th:first-child,
.table td:first-child {
  position: sticky;
  left: 0;
  z-index: 10;
  background: var(--bg-primary);
  border-right: 2px solid var(--border-primary);
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  min-width: 120px;
}

.table th:first-child {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  z-index: 11;
  font-weight: 600;
}

/* Ensure sticky column maintains row hover effects */
.table tr:hover td:first-child {
  background: rgba(148, 226, 213, 0.05);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
}

.table tr:nth-child(even) td:first-child {
  background: rgba(255, 255, 255, 0.01);
}

.table th {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--text-primary);
  padding: 15px;
  text-align: center;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--glass-border);
  position: relative;
}

.table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.4;
}

.table td {
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid var(--border-primary);
  color: var(--text-primary);
  background: transparent;
}

.table tr:hover {
  background: rgba(148, 226, 213, 0.05);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
}

.table tr:nth-child(even) {
  background: rgba(255, 255, 255, 0.01);
}

.rank-badge {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--accent-teal);
  border: 1px solid var(--accent-teal);
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: bold;
  font-size: 12px;
  min-width: 30px;
  display: inline-block;
  box-shadow: var(--subtle-shadow);
  transition: all 0.3s ease;
}

.rank-badge:hover {
  box-shadow: var(--hover-shadow);
  transform: scale(1.05);
}

.crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
  margin-right: 10px;
  box-shadow: var(--subtle-shadow);
  border: 1px solid var(--border-primary);
  transition: all 0.3s ease;
}

.crypto-icon:hover {
  box-shadow: var(--hover-shadow);
  transform: scale(1.05);
}

.symbol-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: var(--text-primary);
}

.signal-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid transparent;
  position: relative;
  overflow: visible;
  text-shadow: 0 0 8px currentColor;
  transition: all 0.3s ease;
  min-height: 32px;
  box-sizing: border-box;
}

/* Custom fast tooltip for signal badges */
.signal-badge-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-surface);
  color: var(--text-primary);
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: normal;
  text-transform: none;
  white-space: nowrap;
  z-index: 1000;
  box-shadow: var(--hover-shadow);
  border: 1px solid var(--border-primary);
  margin-bottom: 8px;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.15s ease-out, transform 0.15s ease-out;
  transform: translateX(-50%) translateY(5px);
}

.signal-badge-tooltip.visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

.signal-badge-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--border-primary);
}

.signal-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: inherit;
  opacity: 0.1;
  z-index: -1;
}

/* Signal Badge States */
.signal-icon {
  font-size: 14px;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
}

.signal-label {
  font-size: 11px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.signal-spinner {
  font-size: 14px;
  animation: spin 1s linear infinite;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 16px;
}

/* Interactive States */
.clickable-signal {
  cursor: pointer;
  transition: all 0.3s ease;
  border-width: 2px;
  box-shadow: var(--subtle-shadow);
}

.clickable-signal:hover {
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--hover-shadow);
  border-color: currentColor;
  backdrop-filter: var(--glass-blur-strong);
  -webkit-backdrop-filter: var(--glass-blur-strong);
}

.clickable-signal:focus {
  outline: none;
  box-shadow: var(--hover-shadow), 0 0 0 3px rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.clickable-signal:active {
  transform: translateY(0) scale(1.02);
  box-shadow: var(--subtle-shadow);
}

/* Loading and Disabled States */
.signal-loading {
  opacity: 0.7;
  cursor: wait;
  pointer-events: none;
}

.signal-disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  filter: grayscale(0.3);
}

/* Signal Color Variants - Semantic Trading Colors */
.signal-gold {
  background: rgba(249, 226, 175, 0.2);
  color: var(--accent-yellow);
  border-color: rgba(249, 226, 175, 0.4);
  box-shadow: var(--subtle-shadow), 0 0 12px rgba(249, 226, 175, 0.3);
}

.signal-gold.clickable-signal {
  background: rgba(249, 226, 175, 0.25);
  border-color: var(--accent-yellow);
}

.signal-gold.clickable-signal:hover {
  background: rgba(249, 226, 175, 0.35);
  box-shadow: var(--hover-shadow), 0 0 20px rgba(249, 226, 175, 0.5);
}

.signal-blue {
  background: rgba(137, 180, 250, 0.2);
  color: var(--accent-blue);
  border-color: rgba(137, 180, 250, 0.4);
  box-shadow: var(--subtle-shadow), 0 0 12px rgba(137, 180, 250, 0.3);
}

.signal-blue.clickable-signal {
  background: rgba(137, 180, 250, 0.25);
  border-color: var(--accent-blue);
}

.signal-blue.clickable-signal:hover {
  background: rgba(137, 180, 250, 0.35);
  box-shadow: var(--hover-shadow), 0 0 20px rgba(137, 180, 250, 0.5);
}

.signal-gray {
  background: rgba(147, 153, 178, 0.2);
  color: var(--text-muted);
  border-color: rgba(147, 153, 178, 0.4);
  box-shadow: var(--subtle-shadow), 0 0 12px rgba(147, 153, 178, 0.2);
}

.signal-gray.clickable-signal {
  background: rgba(147, 153, 178, 0.25);
  border-color: var(--text-muted);
}

.signal-gray.clickable-signal:hover {
  background: rgba(147, 153, 178, 0.35);
  box-shadow: var(--hover-shadow), 0 0 20px rgba(147, 153, 178, 0.4);
}

.signal-default {
  background: rgba(147, 153, 178, 0.15);
  color: var(--text-muted);
  border-color: var(--border-primary);
  box-shadow: var(--subtle-shadow);
}

.loading-container {
  text-align: center;
  padding: 60px 40px;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  margin: 40px auto;
  max-width: 600px;
  box-shadow: var(--glass-shadow);
  color: var(--text-primary);
}

/* Chart Modal Styles */
.chart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: modalFadeIn 0.3s ease-out;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: var(--glass-blur);
  }
}

.chart-modal-content {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur-strong);
  -webkit-backdrop-filter: var(--glass-blur-strong);
  border: 1px solid var(--glass-border);
  border-radius: 24px;
  padding: 24px;
  max-width: 95vw;
  max-height: 75vh;
  width: 95vw;
  height: 75vh;
  box-shadow: var(--glass-shadow);
  position: relative;
  display: flex;
  flex-direction: column;
  color: var(--text-primary);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    transform: translateY(20px) scale(0.95);
    opacity: 0;
  }
  to {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
}

.chart-modal-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--glass-border), transparent);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--glass-border);
  position: relative;
}

.chart-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-cyan), transparent);
}

.chart-header h3 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.3rem;
  font-weight: 600;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.close-button {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  font-size: 20px;
  cursor: pointer;
  color: var(--text-primary);
  padding: 0;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Mobile-specific close button improvements */
@media (max-width: 768px) {
  .close-button {
    width: 48px;
    height: 48px;
    font-size: 24px;
    padding: 12px;
    /* Ensure it's always accessible */
    z-index: 1001;
  }
}

@media (max-width: 480px) {
  .close-button {
    width: 52px;
    height: 52px;
    font-size: 28px;
    padding: 14px;
  }
}

.chart-container {
  margin: 20px 0;
  width: 100%;
  min-width: 600px;
  flex: 1;
  min-height: 400px;
  background: var(--bg-secondary);
  border-radius: 16px;
  border: 1px solid var(--border-primary);
  overflow: hidden;
  position: relative;
  box-shadow: var(--glass-shadow);
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--accent-teal), transparent);
  opacity: 0.3;
}

.chart-legend {
  display: flex;
  gap: 24px;
  margin-top: 20px;
  justify-content: center;
  flex-wrap: wrap;
  padding: 16px;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Mobile Breakpoints - Progressive Enhancement */
@media (max-width: 480px) {
  .app-container {
    padding: 10px;
  }

  .header {
    padding: 12px 16px;
    margin-bottom: 20px;
  }

  .header-main {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }

  .header-title {
    font-size: 1.6rem;
    gap: 8px;
  }

  .lightning-icon {
    font-size: 1.8rem;
  }

  .header-subtitle {
    font-size: 0.85rem;
  }

  .live-indicator {
    padding: 4px 8px;
    font-size: 0.65rem;
  }

  .live-dot {
    width: 4px;
    height: 4px;
  }

  /* Table Container - Enable horizontal scrolling with sticky first column */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    position: relative;
  }

  /* Table - Minimum width to prevent cramping */
  .table {
    min-width: 600px;
    table-layout: auto;
    position: relative;
  }

  /* Sticky first column for mobile */
  .table th:first-child,
  .table td:first-child {
    position: sticky;
    left: 0;
    z-index: 10;
    background: var(--bg-primary);
    border-right: 2px solid var(--border-primary);
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }

  .table th:first-child {
    background: var(--bg-surface);
    z-index: 11;
  }

  .table th, .table td {
    padding: 16px 12px;
    font-size: 14px;
    line-height: 1.4;
    white-space: nowrap;
  }

  .table th {
    font-size: 13px;
    font-weight: 600;
  }

  /* Signal Badge - WCAG AAA compliant touch targets */
  .signal-badge {
    padding: 12px 16px;
    font-size: 14px;
    gap: 8px;
    min-height: 48px;
    min-width: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
  }

  .signal-icon {
    font-size: 16px;
    min-width: 16px;
  }

  .signal-label {
    font-size: 12px;
    font-weight: 500;
  }

  /* Crypto Icon - Larger for better visibility */
  .crypto-icon {
    width: 36px;
    height: 36px;
    font-size: 14px;
  }

  /* Chart Modal - Full screen on small devices */
  .chart-modal-content {
    margin: 5px;
    max-width: calc(100vw - 10px);
    width: calc(100vw - 10px);
    max-height: calc(100vh - 10px);
    height: calc(100vh - 10px);
    padding: 12px;
    border-radius: 16px;
  }

  .chart-container {
    min-width: 280px;
    min-height: 200px;
    margin: 10px 0;
  }

  .chart-legend {
    gap: 8px;
    padding: 8px;
    margin-top: 10px;
    flex-wrap: wrap;
  }

  .legend-item {
    font-size: 12px;
    padding: 4px 8px;
  }

  /* Close button - Larger touch target */
  .chart-close-button {
    min-width: 48px;
    min-height: 48px;
    padding: 12px;
    font-size: 18px;
  }
}

@media (max-width: 768px) and (min-width: 481px) {
  .app-container {
    padding: 15px;
  }

  .header {
    padding: 16px 20px;
    margin-bottom: 24px;
  }

  .header-main {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  .header-title {
    font-size: 1.8rem;
    gap: 10px;
  }

  .lightning-icon {
    font-size: 2rem;
  }

  .header-subtitle {
    font-size: 0.9rem;
  }

  .live-indicator {
    padding: 4px 12px;
    font-size: 0.75rem;
  }

  .live-dot {
    width: 6px;
    height: 6px;
  }

  .stats-info {
    flex-direction: column;
    gap: 15px;
    text-align: center;
    padding: 16px;
  }

  /* Table - Better readability with sticky first column */
  .table-container {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    position: relative;
  }

  .table {
    min-width: 700px;
    position: relative;
  }

  /* Sticky first column for tablets */
  .table th:first-child,
  .table td:first-child {
    position: sticky;
    left: 0;
    z-index: 10;
    background: var(--bg-primary);
    border-right: 2px solid var(--border-primary);
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }

  .table th:first-child {
    background: var(--bg-surface);
    z-index: 11;
  }

  .table th, .table td {
    padding: 14px 10px;
    font-size: 13px;
    line-height: 1.3;
  }

  /* Signal Badge - Improved touch targets */
  .signal-badge {
    padding: 10px 14px;
    font-size: 12px;
    gap: 6px;
    min-height: 44px;
    min-width: 44px;
    border-radius: 10px;
  }

  .signal-icon {
    font-size: 14px;
    min-width: 14px;
  }

  .signal-label {
    font-size: 11px;
  }

  .crypto-icon {
    width: 32px;
    height: 32px;
    font-size: 12px;
  }

  .chart-modal-content {
    margin: 10px;
    max-width: calc(100vw - 20px);
    width: calc(100vw - 20px);
    max-height: calc(85vh - 20px);
    height: calc(85vh - 20px);
    padding: 16px;
    border-radius: 20px;
  }

  .chart-container {
    min-width: 320px;
    min-height: 240px;
    margin: 15px 0;
  }

  .chart-legend {
    gap: 10px;
    padding: 10px;
    margin-top: 12px;
  }

  .legend-item {
    font-size: 12px;
  }
}

/* Landscape Orientation Support */
@media (max-height: 500px) and (orientation: landscape) {
  .app-container {
    padding: 8px;
  }

  .header {
    padding: 8px 16px;
    margin-bottom: 16px;
  }

  .header-main {
    flex-direction: row;
    gap: 16px;
    align-items: center;
  }

  .header-title {
    font-size: 1.4rem;
  }

  .chart-modal-content {
    max-height: calc(100vh - 10px);
    height: calc(100vh - 10px);
    padding: 8px;
  }

  .chart-container {
    min-height: 180px;
    margin: 8px 0;
  }

  .chart-legend {
    margin-top: 8px;
    padding: 6px;
  }
}

/* Touch-friendly improvements */
@media (pointer: coarse) {
  /* Increase touch targets for all interactive elements */
  button, .signal-badge, .chart-close-button {
    min-height: 48px;
    min-width: 48px;
  }

  /* Better spacing for touch interaction */
  .table th, .table td {
    padding: 16px 12px;
  }

  /* Larger text for better readability */
  .table {
    font-size: 14px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .signal-badge, .crypto-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Very small screens - Alternative card layout */
@media (max-width: 360px) {
  .table-container {
    overflow-x: visible;
  }

  .table {
    min-width: auto;
    display: block;
  }

  .table thead {
    display: none;
  }

  .table tbody {
    display: block;
  }

  .table tr {
    display: block;
    margin-bottom: 16px;
    background: var(--glass-bg);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 16px;
  }

  .table td {
    display: block;
    padding: 8px 0;
    border: none;
    font-size: 14px;
  }

  .table td:before {
    content: attr(data-label) ": ";
    font-weight: 600;
    color: var(--text-secondary);
    display: inline-block;
    width: 120px;
  }

  .signal-badge {
    margin-top: 8px;
  }
}

/* Loading State Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--accent-cyan);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-message {
  color: var(--text-secondary);
  font-size: 16px;
  margin: 0;
  font-weight: 500;
}

.loading-small .loading-spinner {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

.loading-large .loading-spinner {
  width: 60px;
  height: 60px;
  border-width: 4px;
}

/* Error State Styles */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 15px;
  color: var(--accent-red, #ef4444);
}

.error-message {
  color: #ef4444;
  font-size: 16px;
  margin: 0 0 20px 0;
  max-width: 400px;
  font-weight: 500;
}

.retry-button {
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  padding: 12px 24px;
  border-radius: 12px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.retry-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

/* Error Boundary Styles */
.error-boundary {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--neon-pink);
  border-radius: 16px;
  margin: 20px;
  box-shadow: var(--glass-shadow), var(--glow-pink);
}

.error-boundary h3 {
  color: var(--neon-pink);
  margin: 0 0 10px 0;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(255, 0, 255, 0.5);
}

.error-boundary p {
  color: var(--text-secondary);
  margin: 0 0 20px 0;
}

/* AMOLED Specific Enhancements */
.table th {
  background: rgba(0, 255, 255, 0.05);
  color: var(--text-primary);
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);
  border-bottom: 1px solid var(--neon-cyan);
}

.table td {
  color: var(--text-primary);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Comfortable Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-teal);
  border-radius: 4px;
  box-shadow: var(--subtle-shadow);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-blue);
  box-shadow: var(--hover-shadow);
}

/* Selection styling */
::selection {
  background: rgba(148, 226, 213, 0.3);
  color: var(--text-primary);
}

::-moz-selection {
  background: rgba(148, 226, 213, 0.3);
  color: var(--text-primary);
}

/* DarkReader Controls Styling */
.darkreader-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  background: var(--glass-bg-strong);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 12px;
  box-shadow: var(--glass-shadow);
  color: var(--text-primary);
  font-size: 12px;
}

.darkreader-main-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.darkreader-toggle {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  padding: 6px 12px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.darkreader-toggle:hover {
  background: var(--glass-bg-strong);
  border-color: var(--accent-teal);
}

.darkreader-toggle.enabled {
  background: rgba(148, 226, 213, 0.2);
  border-color: var(--accent-teal);
  color: var(--accent-teal);
}

.darkreader-settings-toggle {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  padding: 6px 8px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 11px;
  transition: all 0.3s ease;
}

.darkreader-settings-toggle:hover {
  background: var(--glass-bg-strong);
  border-color: var(--accent-blue);
}

.darkreader-advanced-controls {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--glass-border);
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 200px;
}

.darkreader-control-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.darkreader-control-group label {
  font-size: 10px;
  color: var(--text-secondary);
  font-weight: 500;
}

.darkreader-slider {
  width: 100%;
  height: 4px;
  background: var(--bg-surface);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
  appearance: none;
}

.darkreader-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: var(--accent-teal);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.darkreader-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: var(--accent-teal);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.darkreader-presets {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  margin-top: 8px;
}

.darkreader-preset {
  background: var(--glass-bg);
  border: 1px solid var(--glass-border);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 9px;
  transition: all 0.3s ease;
  flex: 1;
  min-width: 0;
}

.darkreader-preset:hover {
  background: var(--glass-bg-strong);
  border-color: var(--accent-purple);
  color: var(--accent-purple);
}

/* Mobile DarkReader controls optimization */
@media (max-width: 768px) {
  .darkreader-controls {
    top: 10px;
    right: 10px;
    padding: 8px;
    font-size: 11px;
  }

  .darkreader-toggle, .darkreader-settings-toggle {
    padding: 8px 12px;
    font-size: 12px;
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .darkreader-advanced-controls {
    min-width: 160px;
  }

  .darkreader-slider::-webkit-slider-thumb {
    width: 16px;
    height: 16px;
  }

  .darkreader-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
  }

  .darkreader-preset {
    padding: 8px 12px;
    font-size: 10px;
    min-height: 36px;
  }
}

@media (max-width: 480px) {
  .darkreader-controls {
    top: 5px;
    right: 5px;
    padding: 6px;
    font-size: 10px;
  }

  .darkreader-toggle, .darkreader-settings-toggle {
    padding: 10px 14px;
    font-size: 11px;
    min-height: 48px;
    min-width: 48px;
  }

  .darkreader-advanced-controls {
    min-width: 140px;
  }
}
